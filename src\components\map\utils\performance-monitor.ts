/**
 * 地图性能监控工具
 * 用于监控主题切换和图层渲染的性能
 */

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  details?: any;
}

class MapPerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean = false;

  constructor() {
    // 在开发环境中启用性能监控
    this.isEnabled = process.env.NODE_ENV === "development";
  }

  /**
   * 开始性能测量
   */
  start(name: string, details?: any): void {
    if (!this.isEnabled) {
      return;
    }

    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      details,
    };

    this.metrics.set(name, metric);
    console.log(`🚀 开始测量: ${name}`, details);
  }

  /**
   * 结束性能测量
   */
  end(name: string, additionalDetails?: any): number | null {
    if (!this.isEnabled) {
      return null;
    }

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`⚠️ 未找到性能测量: ${name}`);
      return null;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;

    if (additionalDetails) {
      metric.details = { ...metric.details, ...additionalDetails };
    }

    // 根据持续时间显示不同的日志级别
    const duration = metric.duration;
    const emoji = duration < 100 ? "✅" : duration < 500 ? "⚡" : "🐌";

    console.log(
      `${emoji} 完成测量: ${name} - ${duration.toFixed(2)}ms`,
      metric.details
    );

    return duration;
  }

  /**
   * 测量异步操作
   */
  async measure<T>(
    name: string,
    operation: () => Promise<T>,
    details?: any
  ): Promise<T> {
    if (!this.isEnabled) {
      return await operation();
    }

    this.start(name, details);
    try {
      const result = await operation();
      this.end(name, { success: true });
      return result;
    } catch (error: any) {
      this.end(name, { success: false, error: error.message });
      throw error;
    }
  }

  /**
   * 测量同步操作
   */
  measureSync<T>(name: string, operation: () => T, details?: any): T {
    if (!this.isEnabled) {
      return operation();
    }

    this.start(name, details);
    try {
      const result = operation();
      this.end(name, { success: true });
      return result;
    } catch (error: any) {
      this.end(name, { success: false, error: error.message });
      throw error;
    }
  }

  /**
   * 获取所有性能指标
   */
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values()).filter(
      (m) => m.duration !== undefined
    );
  }

  /**
   * 获取性能统计
   */
  getStats(): {
    totalMeasurements: number;
    averageDuration: number;
    slowestOperation: PerformanceMetric | null;
    fastestOperation: PerformanceMetric | null;
  } {
    const completedMetrics = this.getMetrics();

    if (completedMetrics.length === 0) {
      return {
        totalMeasurements: 0,
        averageDuration: 0,
        slowestOperation: null,
        fastestOperation: null,
      };
    }

    const durations = completedMetrics.map((m) => m.duration!);
    const totalDuration = durations.reduce((sum, d) => sum + d, 0);
    const averageDuration = totalDuration / durations.length;

    const slowestOperation = completedMetrics.reduce((slowest, current) =>
      current.duration! > slowest.duration! ? current : slowest
    );

    const fastestOperation = completedMetrics.reduce((fastest, current) =>
      current.duration! < fastest.duration! ? current : fastest
    );

    return {
      totalMeasurements: completedMetrics.length,
      averageDuration,
      slowestOperation,
      fastestOperation,
    };
  }

  /**
   * 清除所有指标
   */
  clear(): void {
    this.metrics.clear();
    console.log("🧹 性能指标已清除");
  }

  /**
   * 打印性能报告
   */
  printReport(): void {
    if (!this.isEnabled) {
      return;
    }

    const stats = this.getStats();

    console.group("📊 地图性能报告");
    console.log(`总测量次数: ${stats.totalMeasurements}`);
    console.log(`平均持续时间: ${stats.averageDuration.toFixed(2)}ms`);

    if (stats.slowestOperation) {
      console.log(
        `最慢操作: ${
          stats.slowestOperation.name
        } (${stats.slowestOperation.duration!.toFixed(2)}ms)`
      );
    }

    if (stats.fastestOperation) {
      console.log(
        `最快操作: ${
          stats.fastestOperation.name
        } (${stats.fastestOperation.duration!.toFixed(2)}ms)`
      );
    }

    // 显示所有测量结果
    const metrics = this.getMetrics();
    if (metrics.length > 0) {
      console.table(
        metrics.map((m) => ({
          操作: m.name,
          持续时间: `${m.duration!.toFixed(2)}ms`,
          详情: JSON.stringify(m.details || {}),
        }))
      );
    }

    console.groupEnd();
  }

  /**
   * 启用/禁用性能监控
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    console.log(`🔧 性能监控已${enabled ? "启用" : "禁用"}`);
  }
}

// 创建全局实例
export const mapPerformanceMonitor = new MapPerformanceMonitor();

/**
 * 主题切换性能监控装饰器
 */
export function monitorThemeSwitch() {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const operationName = `主题切换-${propertyName}`;
      return await mapPerformanceMonitor.measure(
        operationName,
        () => method.apply(this, args),
        {
          method: propertyName,
          args: args.length,
          timestamp: new Date().toISOString(),
        }
      );
    };

    return descriptor;
  };
}

/**
 * 图层操作性能监控装饰器
 */
export function monitorLayerOperation(operationType: string) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const method = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const operationName = `图层操作-${operationType}-${propertyName}`;
      return mapPerformanceMonitor.measureSync(
        operationName,
        () => method.apply(this, args),
        {
          type: operationType,
          method: propertyName,
          args: args.length,
        }
      );
    };

    return descriptor;
  };
}

/**
 * 快速性能检查函数
 */
export const quickPerformanceCheck = {
  /**
   * 检查主题切换性能
   */
  themeSwitch: () => {
    const startTime = performance.now();

    return {
      end: () => {
        const duration = performance.now() - startTime;
        const status =
          duration < 100
            ? "优秀"
            : duration < 300
            ? "良好"
            : duration < 500
            ? "一般"
            : "需要优化";

        console.log(`🎨 主题切换性能: ${duration.toFixed(2)}ms (${status})`);
        return { duration, status };
      },
    };
  },

  /**
   * 检查图层渲染性能
   */
  layerRender: (layerType: string) => {
    const startTime = performance.now();

    return {
      end: () => {
        const duration = performance.now() - startTime;
        const status =
          duration < 50
            ? "优秀"
            : duration < 150
            ? "良好"
            : duration < 300
            ? "一般"
            : "需要优化";

        console.log(
          `🗺️ ${layerType}图层渲染: ${duration.toFixed(2)}ms (${status})`
        );
        return { duration, status };
      },
    };
  },
};

// 在开发环境中自动启用性能监控
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  (window as any).mapPerformanceMonitor = mapPerformanceMonitor;
  (window as any).quickPerformanceCheck = quickPerformanceCheck;

  console.log("🔧 地图性能监控工具已加载到 window.mapPerformanceMonitor");
  console.log("🔧 快速性能检查工具已加载到 window.quickPerformanceCheck");
}
